package manager.dao;

import manager.common.BaseDao;
import manager.pojo.CheckItem;
import manager.util.CommonUtil;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class CheckItemDao {
    public static final Object[] columnNames = {"ID","编号","名称","参考值","单位","创建时间","更新时间","删除时间","创建人","状态"};

    //根据名称模糊查询
    public static Object[][] quarryAllCheckItem(String cname,String ccode) {
        List<CheckItem> list = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("select * from checkitem ");

        List<Object> params = new ArrayList<>();
        boolean hasCname = cname != null && !cname.isEmpty();
        boolean hasCcode = ccode != null && !ccode.isEmpty();

        //添加条件
        if(hasCcode || hasCname){
            sql.append("where ");
            if(hasCname){
                sql.append("cname like ?");
                params.add("%" + cname + "%");
            }
            if (hasCcode) {
                if(hasCname){
                    sql.append("and ");
                }
                sql.append("ccode like ?");
                params.add("%"+ ccode + "%");
            }
        }
        Object[] obj = params.toArray();

        //执行查询
        ResultSet resultSet = BaseDao.executeDQL(sql.toString(), obj);
        try {
            while (resultSet.next()) {
                CheckItem checkItem = new CheckItem(
                        resultSet.getInt("cid"),
                        resultSet.getString("ccode"),
                        resultSet.getString("cname"),
                        resultSet.getString("refer_val"),
                        resultSet.getString("unit"),
                        resultSet.getDate("create_date"),
                        resultSet.getDate("upd_date"),
                        resultSet.getDate("delete_date"),
                        resultSet.getString("option_user"),
                        resultSet.getString("status")
                );
            }
        }
        catch (SQLException e) {
            e.printStackTrace();
        }
        return CommonUtil.toArray(list);
    }

    //添加检查项
    public static int addCheckItem(CheckItem checkItem){
        String sql = "INSERT INTO checkitem(ccode, cname, refer_val, unit, create_date, option_user, status) VALUES (?,?,?,now(),?,1)";
        Object[] params = {
                checkItem.getCcode(),
                checkItem.getCname(),
                checkItem.getReferVal(),
                checkItem.getUnit(),
                checkItem.getOptionUser()
        };
        //执行DML语句更新操作
        return BaseDao.executeDML(sql,params);
    }

    public static CheckItem queryCheckItemById(String ccode){
        String sql = "SELECT * FROM checkitem WHERE ccode = ?";
        Object[] params = {ccode};
        ResultSet resultSet = BaseDao.executeDQL(sql,params);
        CheckItem checkItem = null;
        try{
            while (resultSet.next()){
                checkItem = new CheckItem(
                        resultSet.getInt("cid"),
                        resultSet.getString("ccode"),
                        resultSet.getString("cname"),
                        resultSet.getString("refer_val"),
                        resultSet.getString("unit"),
                        resultSet.getDate("create_date"),
                        resultSet.getDate("upd_date"),
                        resultSet.getDate("delete_date"),
                        resultSet.getString("option_user"),
                        resultSet.getString("status")
                );
            }
        } catch (SQLException e){
            e.printStackTrace();
        }
        return checkItem;
    }
}
