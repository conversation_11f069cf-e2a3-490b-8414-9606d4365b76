package manager.frame.admin;

import manager.dao.CheckItemDao;
import manager.frame.MainFrame;
import manager.pojo.CheckItem;
import manager.util.SystemVerifier;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

public class DataEditPanel extends JInternalFrame {
    //添加修改数据的页面，添加时id为null
    public DataEditPanel(Integer id){
        super("数据列表",true,true,true,true);
        this.setVisible(true);
        JPanel panel = new JPanel();
        panel.setBackground(Color.white);
        this.add(panel);
        Box box = Box.createVerticalBox();
        panel.add(box);
        box.add(Box.createVerticalStrut(55));

        //编号输入行
        Box box1 = Box.createHorizontalBox();
        box1.add(new JLabel("代号：*"));
        JTextField field1 = new JTextField(10);

        //设置输入验证器
        field1.setInputVerifier(SystemVerifier.emptyVerify("代号",2,null));
        box1.add(field1);
        box.add(box1);
        box.add(Box.createVerticalStrut(5));

        //名称输入行
        Box box2 = Box.createHorizontalBox();
        box2.add(new JLabel("名称：*"));
        JTextField field2 = new JTextField(10);
        box2.add(field2);
        box.add(box2);
        box.add(Box.createVerticalStrut(5));

        //参考值输入行
        Box box3 = Box.createHorizontalBox();
        box3.add(new JLabel("参考值：*"));
        JTextField field3 = new JTextField(10);
        box3.add(field3);
        box.add(box3);
        box.add(Box.createVerticalStrut(5));

        //单位输入行
        Box box4 = Box.createHorizontalBox();
        box4.add(new JLabel("单位：*"));
        JTextField filed4 = new JTextField(10);
        box4.add(filed4);
        box.add(box4);
        box.add(Box.createVerticalStrut(5));

        //提交按钮容器
        JButton btn = new JButton("提交");
        Box boxBtn = Box.createHorizontalBox();
        boxBtn.add(Box.createHorizontalStrut(40));
        boxBtn.add(btn);
        box.add(boxBtn);

        //该提交按钮添加监听器
        btn.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                //点击时触发校验，如果不成功就不添加
                if(!field1.getInputVerifier().verify(field1)){
                    return;
                }

                CheckItem checkItem = new CheckItem(id,field1.getText(),field2.getText(),field3.getText(),filed4.getText(), MainFrame.users.getUname());

                CheckItem checkItem1 = new CheckItem(field1.getText());
                if(checkItem1 != null){
                    JOptionPane.showMessageDialog(btn.getParent(),"检查项编号重复，无法添加","系统提示",JOptionPane.WARNING_MESSAGE);
                    return;
                }

                int i = CheckItemDao.addCheckItem(checkItem);
                if(i>0){
                    System.out.println("添加成功");
                    AdminPanel.setContent(new DataTablePanel());
                }else
                {
                    JOptionPane.showMessageDialog(btn.getParent(),"检查项添加失败","系统提示",JOptionPane.WARNING_MESSAGE);
                }
            }
        });
    }
}
